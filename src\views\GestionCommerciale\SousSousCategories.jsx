import React, { useEffect, useState } from 'react';
import {
  fetchAllSousSousCategories,
  createSousSousCategorie,
  updateSousSousCategorie,
  deleteSousSousCategorie,
  fetchAllSousCategories
} from '../../services/categoryService';
import { Table, Button, Form, Alert, Spinner, Container, Row, Col, Card, Toast, ToastContainer } from 'react-bootstrap';

const initialForm = { nom: '', description: '', sous_categorie_id: '' };

export default function SousSousCategories({ ImageManager }) {
  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [form, setForm] = useState(initialForm);
  const [editingId, setEditingId] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [toast, setToast] = useState({ show: false, message: '', variant: 'success' });

  const loadSousSousCategories = async () => {
    setLoading(true);
    setError('');
    try {
      const data = await fetchAllSousSousCategories();
      setSousSousCategories(data);
    } catch (e) {
      setError(e.message);
    }
    setLoading(false);
  };

  const loadSousCategories = async () => {
    try {
      const data = await fetchAllSousCategories();
      setSousCategories(data);
    } catch (e) {
      // Optionally show error
    }
  };

  useEffect(() => {
    loadSousSousCategories();
    loadSousCategories();
  }, []);

  const handleChange = (e) => {
    const newForm = { ...form, [e.target.name]: e.target.value };
    console.log(`📝 Sous-sous-category form field changed: ${e.target.name} = ${e.target.value}`);
    console.log('📋 Updated sous-sous-category form:', newForm);
    setForm(newForm);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      console.log(`📝 ${editingId ? 'Updating' : 'Creating'} sous-sous-category:`, form);

      if (editingId) {
        console.log(`🔄 Updating sous-sous-category with ID: ${editingId}`);
        await updateSousSousCategorie(editingId, form);
        console.log('✅ Sous-sous-category updated successfully');
        setToast({ show: true, message: 'Sous-sous-catégorie mise à jour !', variant: 'success' });
      } else {
        console.log('➕ Creating new sous-sous-category');
        await createSousSousCategorie(form);
        console.log('✅ Sous-sous-category created successfully');
        setToast({ show: true, message: 'Sous-sous-catégorie ajoutée !', variant: 'success' });
      }

      setForm(initialForm);
      setEditingId(null);
      loadSousSousCategories();
    } catch (e) {
      console.error('❌ Error submitting sous-sous-category:', e);
      setError(e.message);
      setToast({ show: true, message: e.message, variant: 'danger' });
    }
    setSubmitting(false);
  };

  const handleEdit = (ssc) => {
    console.log('✏️ Opening sous-sous-category edit for:', ssc);
    const formData = {
      nom: ssc.nom || ssc.nom_sous_sous_categorie,
      description: ssc.description || ssc.description_sous_sous_categorie,
      sous_categorie_id: ssc.sous_categorie_id
    };
    console.log('📝 Setting sous-sous-category form data:', formData);
    setForm(formData);
    setEditingId(ssc.id);
  };

  const handleDelete = async (id, name) => {
    if (
      !window.confirm(
        `Supprimer la sous-sous-catégorie "${name}" ?\n\nCette action supprimera également tous les produits associés et est irréversible.`
      )
    )
      return;

    setError('');
    console.log(`🗑️ Starting delete process for sous-sous-category with ID: ${id}`);

    try {
      await deleteSousSousCategorie(id);
      console.log('✅ Sous-sous-category deleted, reloading data...');
      setToast({ show: true, message: 'Sous-sous-catégorie supprimée !', variant: 'success' });
      loadSousSousCategories();
    } catch (e) {
      console.error('❌ Delete operation failed:', e);
      setError(e.message);
      setToast({ show: true, message: e.message, variant: 'danger' });
    }
  };

  return (
    <div>
      {error && <Alert variant="danger">{error}</Alert>}
      <Card className="mb-4 shadow-sm border-0">
        <Card.Header className="bg-white py-3">
          <h5 className="mb-0 fw-bold">{editingId ? 'Modifier une sous-sous-catégorie' : 'Ajouter une nouvelle sous-sous-catégorie'}</h5>
        </Card.Header>
        <Card.Body>
          <Form onSubmit={handleSubmit}>
            <Row className="g-3">
              <Col xs={12} md={4}>
                <Form.Group controlId="sousSousCategorieNom">
                  <Form.Label className="fw-medium">Nom de la sous-sous-catégorie</Form.Label>
                  <Form.Control
                    name="nom"
                    value={form.nom}
                    onChange={handleChange}
                    placeholder="Nom de la sous-sous-catégorie"
                    required
                    disabled={submitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
                </Form.Group>
              </Col>
              <Col xs={12} md={4}>
                <Form.Group controlId="sousSousCategorieDescription">
                  <Form.Label className="fw-medium">Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={form.description}
                    onChange={handleChange}
                    placeholder="Description détaillée de la sous-sous-catégorie"
                    disabled={submitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
              <Col xs={12} md={4}>
                <Form.Group controlId="sousCategorieId">
                  <Form.Label className="fw-medium">Sous-catégorie parente</Form.Label>
                  <Form.Select
                    name="sous_categorie_id"
                    value={form.sous_categorie_id}
                    onChange={handleChange}
                    required
                    disabled={submitting || sousCategories.length === 0}
                    className="rounded-3 border-2"
                  >
                    <option value="">Sélectionnez une sous-catégorie</option>
                    {sousCategories.map((sc) => (
                      <option key={sc.id} value={sc.id}>
                        {sc.nom || sc.nom_sous_categorie || `ID ${sc.id}`}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Sélectionnez la sous-catégorie à laquelle cette sous-sous-catégorie appartient.
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>
            <div className="d-flex gap-2 mt-4 justify-content-end">
              {editingId && (
                <Button
                  variant="outline-secondary"
                  type="button"
                  onClick={() => {
                    setForm(initialForm);
                    setEditingId(null);
                  }}
                  className="px-4"
                >
                  <i className="bi bi-x-circle me-2"></i>
                  Annuler
                </Button>
              )}
              <Button type="submit" variant={editingId ? 'warning' : 'info'} disabled={submitting} className="px-4">
                {submitting ? (
                  <>
                    <Spinner size="sm" animation="border" className="me-2" />
                    Traitement...
                  </>
                ) : editingId ? (
                  <>
                    <i className="bi bi-pencil-square me-2" />
                    Mettre à jour
                  </>
                ) : (
                  <>
                    <i className="bi bi-plus-circle me-2" />
                    Ajouter
                  </>
                )}
              </Button>
            </div>
          </Form>
          {/* Image management for selected sous-sous-categorie */}
          {editingId && ImageManager && (
            <div className="mt-4 pt-3 border-top">
              <h6 className="mb-3 text-muted">Gestion des images</h6>
              <ImageManager modelType="sous_sous_categorie" modelId={editingId} />
            </div>
          )}
        </Card.Body>
      </Card>
      <Card className="border-0 shadow-sm">
        <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
          <h5 className="mb-0 fw-bold">Liste des sous-sous-catégories</h5>
          <div className="text-muted small">
            {sousSousCategories.length} sous-sous-catégorie{sousSousCategories.length !== 1 ? 's' : ''} au total
          </div>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="info" />
              <p className="mt-3 text-muted">Chargement des sous-sous-catégories...</p>
            </div>
          ) : sousSousCategories.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <i className="bi bi-folder text-muted" style={{ fontSize: '3rem' }}></i>
              </div>
              <p className="text-muted">Aucune sous-sous-catégorie trouvée.</p>
              <Button variant="info" onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>
                <i className="bi bi-plus-circle me-2"></i>
                Créer une sous-sous-catégorie
              </Button>
            </div>
          ) : (
            <div className="table-responsive">
              <Table hover responsive className="align-middle mb-0">
                <thead>
                  <tr className="bg-light">
                    <th className="ps-3" style={{ width: '60px' }}>
                      ID
                    </th>
                    <th style={{ width: '20%' }}>Nom</th>
                    <th style={{ width: '30%' }}>Description</th>
                    <th style={{ width: '25%' }}>Sous-catégorie parente</th>
                    <th className="text-end pe-3" style={{ width: '25%' }}>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sousSousCategories.map((ssc) => {
                    const parent = sousCategories.find((sc) => sc.id === ssc.sous_categorie_id);
                    return (
                      <tr key={ssc.id} className="border-bottom">
                        <td className="ps-3 fw-medium">{ssc.id}</td>
                        <td>
                          <div className="d-flex align-items-center">
                            <div className="color-dot bg-info me-2"></div>
                            <span className="fw-medium">{ssc.nom || ssc.nom_sous_sous_categorie}</span>
                          </div>
                        </td>
                        <td>
                          <div className="text-truncate" style={{ maxWidth: '300px' }}>
                            {ssc.description || ssc.description_sous_sous_categorie || (
                              <span className="text-muted fst-italic">Aucune description</span>
                            )}
                          </div>
                        </td>
                        <td>
                          {parent ? (
                            <div className="d-flex align-items-center">
                              <div className="color-dot bg-success me-2"></div>
                              <span>{parent.nom || parent.nom_sous_categorie}</span>
                            </div>
                          ) : (
                            <span className="text-muted">{ssc.sous_categorie_id}</span>
                          )}
                        </td>
                        <td className="text-end pe-3">
                          <Button
                            size="sm"
                            variant="outline-primary"
                            className="me-2 rounded-pill"
                            onClick={() => handleEdit(ssc)}
                            title="Éditer la sous-sous-catégorie"
                          >
                            <i className="bi bi-pencil me-1"></i> Éditer
                          </Button>
                          <Button
                            size="sm"
                            variant="outline-danger"
                            className="rounded-pill"
                            onClick={() => handleDelete(ssc.id, ssc.nom || ssc.nom_sous_sous_categorie)}
                            title="Supprimer la sous-sous-catégorie"
                          >
                            <i className="bi bi-trash me-1"></i> Supprimer
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Custom CSS for table styling */}
      <style jsx="true">{`
        .color-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
      `}</style>
      <ToastContainer position="bottom-end" className="p-3">
        <Toast bg={toast.variant} show={toast.show} onClose={() => setToast({ ...toast, show: false })} delay={3000} autohide>
          <Toast.Body className="text-white">{toast.message}</Toast.Body>
        </Toast>
      </ToastContainer>
    </div>
  );
}
