import React, { useEffect, useState } from 'react';
import {
  fetchAllSousSousCategories,
  createSousSousCategorie,
  updateSousSousCategorie,
  deleteSousSousCategorie,
  fetchAllSousCategories
} from '../../services/categoryService';
import {
  Table,
  Button,
  Form,
  Alert,
  Spinner,
  Container,
  Row,
  Col,
  Card,
  Toast,
  ToastContainer,
  Modal,
  Badge,
  Breadcrumb
} from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaHome, FaLayerGroup } from 'react-icons/fa';

const initialForm = { nom: '', description: '', sous_categorie_id: '' };

export default function SousSousCategories({ ImageManager }) {
  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [form, setForm] = useState(initialForm);
  const [editingId, setEditingId] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [toast, setToast] = useState({ show: false, message: '', variant: 'success' });

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'

  // Delete confirmation modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const loadSousSousCategories = async () => {
    setLoading(true);
    setError('');
    try {
      const data = await fetchAllSousSousCategories();
      setSousSousCategories(data);
    } catch (e) {
      setError(e.message);
    }
    setLoading(false);
  };

  const loadSousCategories = async () => {
    try {
      const data = await fetchAllSousCategories();
      setSousCategories(data);
    } catch (e) {
      // Optionally show error
    }
  };

  useEffect(() => {
    loadSousSousCategories();
    loadSousCategories();
  }, []);

  const handleChange = (e) => {
    const newForm = { ...form, [e.target.name]: e.target.value };
    console.log(`📝 Sous-sous-category form field changed: ${e.target.name} = ${e.target.value}`);
    console.log('📋 Updated sous-sous-category form:', newForm);
    setForm(newForm);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      console.log(`📝 ${editingId ? 'Updating' : 'Creating'} sous-sous-category:`, form);

      if (editingId) {
        console.log(`🔄 Updating sous-sous-category with ID: ${editingId}`);
        await updateSousSousCategorie(editingId, form);
        console.log('✅ Sous-sous-category updated successfully');
        setToast({ show: true, message: 'Sous-sous-catégorie mise à jour !', variant: 'success' });
      } else {
        console.log('➕ Creating new sous-sous-category');
        await createSousSousCategorie(form);
        console.log('✅ Sous-sous-category created successfully');
        setToast({ show: true, message: 'Sous-sous-catégorie ajoutée !', variant: 'success' });
      }

      setForm(initialForm);
      setEditingId(null);
      setShowModal(false);
      loadSousSousCategories();
    } catch (e) {
      console.error('❌ Error submitting sous-sous-category:', e);
      setError(e.message);
      setToast({ show: true, message: e.message, variant: 'danger' });
    }
    setSubmitting(false);
  };

  // Open modal for creating
  const handleCreate = () => {
    setModalAction('create');
    setEditingId(null);
    setForm(initialForm);
    setShowModal(true);
  };

  // Open modal for editing
  const handleEdit = (ssc) => {
    console.log('✏️ Opening sous-sous-category edit modal for:', ssc);
    setModalAction('edit');
    const formData = {
      nom: ssc.nom || ssc.nom_sous_sous_categorie,
      description: ssc.description || ssc.description_sous_sous_categorie,
      sous_categorie_id: ssc.sous_categorie_id
    };
    console.log('📝 Setting sous-sous-category form data:', formData);
    setForm(formData);
    setEditingId(ssc.id);
    setShowModal(true);
  };

  // Close modal
  const handleModalClose = () => {
    setShowModal(false);
    setEditingId(null);
    setForm(initialForm);
    setError('');
  };

  // Confirm delete
  const confirmDelete = (id, name) => {
    setItemToDelete({ id, name });
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!itemToDelete) return;

    setDeleteLoading(true);
    setError('');

    try {
      const { id } = itemToDelete;
      console.log(`🗑️ Starting delete process for sous-sous-category with ID: ${id}`);

      await deleteSousSousCategorie(id);
      console.log('✅ Sous-sous-category deleted, reloading data...');

      setShowDeleteModal(false);
      setItemToDelete(null);
      setToast({ show: true, message: 'Sous-sous-catégorie supprimée !', variant: 'success' });
      loadSousSousCategories();
    } catch (e) {
      console.error('❌ Delete operation failed:', e);
      setError(e.message);
      setToast({ show: true, message: e.message, variant: 'danger' });
    }

    setDeleteLoading(false);
  };

  // Close delete modal
  const handleDeleteModalClose = () => {
    if (!deleteLoading) {
      setShowDeleteModal(false);
      setItemToDelete(null);
    }
  };

  return (
    <Container fluid className="py-4">
      {/* Header and Breadcrumb */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaLayerGroup className="me-2" />
          Gestion des Sous-Sous-Catégories
        </h2>
        <Breadcrumb>
          <Breadcrumb.Item href="/dashboard">
            <FaHome size={14} className="me-1" /> Accueil
          </Breadcrumb.Item>
          <Breadcrumb.Item href="/categories">Gestion des Catégories</Breadcrumb.Item>
          <Breadcrumb.Item active>Sous-Sous-Catégories</Breadcrumb.Item>
        </Breadcrumb>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}

      <Card className="border-0 shadow-sm">
        <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
          <h5 className="mb-0 fw-bold">Liste des sous-sous-catégories</h5>
          <div className="d-flex align-items-center gap-3">
            <div className="text-muted small">
              {sousSousCategories.length} sous-sous-catégorie{sousSousCategories.length !== 1 ? 's' : ''} au total
            </div>
            <Button variant="info" size="sm" onClick={handleCreate}>
              <FaPlus className="me-1" />
              Ajouter une sous-sous-catégorie
            </Button>
          </div>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="info" />
              <p className="mt-3 text-muted">Chargement des sous-sous-catégories...</p>
            </div>
          ) : sousSousCategories.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <i className="bi bi-folder text-muted" style={{ fontSize: '3rem' }}></i>
              </div>
              <p className="text-muted">Aucune sous-sous-catégorie trouvée.</p>
              <Button variant="info" onClick={handleCreate}>
                <FaPlus className="me-2" />
                Créer une sous-sous-catégorie
              </Button>
            </div>
          ) : (
            <div className="table-responsive">
              <Table hover responsive className="align-middle mb-0">
                <thead>
                  <tr className="bg-light">
                    <th className="ps-3" style={{ width: '60px' }}>
                      ID
                    </th>
                    <th style={{ width: '20%' }}>Nom</th>
                    <th style={{ width: '30%' }}>Description</th>
                    <th style={{ width: '25%' }}>Sous-catégorie parente</th>
                    <th className="text-end pe-3" style={{ width: '25%' }}>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sousSousCategories.map((ssc) => {
                    const parent = sousCategories.find((sc) => sc.id === parseInt(ssc.sous_categorie_id));
                    return (
                      <tr key={ssc.id} className="border-bottom">
                        <td className="ps-3 fw-medium">{ssc.id}</td>
                        <td>
                          <div className="d-flex align-items-center">
                            <div className="color-dot bg-info me-2"></div>
                            <span className="fw-medium">{ssc.nom || ssc.nom_sous_sous_categorie}</span>
                          </div>
                        </td>
                        <td>
                          <div className="text-truncate" style={{ maxWidth: '300px' }}>
                            {ssc.description || ssc.description_sous_sous_categorie || (
                              <span className="text-muted fst-italic">Aucune description</span>
                            )}
                          </div>
                        </td>
                        <td>
                          {parent ? (
                            <div className="d-flex align-items-center">
                              <div className="color-dot bg-success me-2"></div>
                              <span>{parent.nom || parent.nom_sous_categorie}</span>
                            </div>
                          ) : (
                            <span className="text-muted">ID: {ssc.sous_categorie_id}</span>
                          )}
                        </td>
                        <td className="text-end pe-3">
                          <Button
                            size="sm"
                            variant="outline-primary"
                            className="me-2 rounded-pill"
                            onClick={() => handleEdit(ssc)}
                            title="Éditer la sous-sous-catégorie"
                          >
                            <FaPencilAlt className="me-1" /> Éditer
                          </Button>
                          <Button
                            size="sm"
                            variant="outline-danger"
                            className="rounded-pill"
                            onClick={() => confirmDelete(ssc.id, ssc.nom || ssc.nom_sous_sous_categorie)}
                            title="Supprimer la sous-sous-catégorie"
                          >
                            <FaTrashAlt className="me-1" /> Supprimer
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Edit/Create Modal */}
      <Modal show={showModal} onHide={handleModalClose} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className={`fas ${modalAction === 'edit' ? 'fa-edit' : 'fa-plus'} me-2`}></i>
            {modalAction === 'edit' ? 'Modifier une sous-sous-catégorie' : 'Ajouter une nouvelle sous-sous-catégorie'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form id="sousSousCatForm" onSubmit={handleSubmit}>
            <Row className="g-3">
              <Col xs={12} md={6}>
                <Form.Group controlId="sousSousCategorieNom">
                  <Form.Label className="fw-medium">Nom de la sous-sous-catégorie</Form.Label>
                  <Form.Control
                    name="nom"
                    value={form.nom || ''}
                    onChange={handleChange}
                    placeholder="Nom de la sous-sous-catégorie"
                    required
                    disabled={submitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
                </Form.Group>
              </Col>
              <Col xs={12} md={6}>
                <Form.Group controlId="sousCategorieId">
                  <Form.Label className="fw-medium">Sous-catégorie parente</Form.Label>
                  <Form.Select
                    name="sous_categorie_id"
                    value={form.sous_categorie_id || ''}
                    onChange={handleChange}
                    required
                    disabled={submitting || sousCategories.length === 0}
                    className="rounded-3 border-2"
                  >
                    <option value="">Sélectionnez une sous-catégorie</option>
                    {sousCategories.map((sc) => (
                      <option key={sc.id} value={sc.id}>
                        {sc.nom || sc.nom_sous_categorie || `ID ${sc.id}`}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Sélectionnez la sous-catégorie à laquelle cette sous-sous-catégorie appartient.
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>
            <Row className="mt-3">
              <Col xs={12}>
                <Form.Group controlId="sousSousCategorieDescription">
                  <Form.Label className="fw-medium">Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={form.description || ''}
                    onChange={handleChange}
                    placeholder="Description détaillée de la sous-sous-catégorie"
                    disabled={submitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleModalClose} disabled={submitting}>
            Annuler
          </Button>
          <Button type="submit" form="sousSousCatForm" variant={modalAction === 'edit' ? 'warning' : 'info'} disabled={submitting}>
            {submitting ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Traitement...
              </>
            ) : modalAction === 'edit' ? (
              <>
                <FaPencilAlt className="me-2" />
                Mettre à jour
              </>
            ) : (
              <>
                <FaPlus className="me-2" />
                Ajouter
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={handleDeleteModalClose} centered>
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="text-danger">
            <FaTrashAlt className="me-2" />
            Confirmer la suppression
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="pt-0">
          {itemToDelete && (
            <div className="text-center py-3">
              <div className="mb-3">
                <div
                  className="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center"
                  style={{ width: '60px', height: '60px' }}
                >
                  <FaTrashAlt className="text-danger" size={24} />
                </div>
              </div>
              <h5 className="mb-3">Êtes-vous sûr de vouloir supprimer ?</h5>
              <p className="text-muted mb-0">
                <strong>Sous-sous-catégorie :</strong> {itemToDelete.name}
              </p>
              <p className="text-muted small mt-2">
                Cette action supprimera également tous les produits associés à cette sous-sous-catégorie.
              </p>
              <div className="alert alert-warning mt-3 mb-0">
                <small>
                  <strong>⚠️ Attention :</strong> Cette action est irréversible.
                </small>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="border-0 pt-0">
          <Button variant="secondary" onClick={handleDeleteModalClose} disabled={deleteLoading}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleDelete} disabled={deleteLoading}>
            {deleteLoading ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Suppression...
              </>
            ) : (
              <>
                <FaTrashAlt className="me-2" />
                Supprimer définitivement
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Custom CSS for table styling */}
      <style jsx="true">{`
        .color-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
      `}</style>

      <ToastContainer position="bottom-end" className="p-3">
        <Toast bg={toast.variant} show={toast.show} onClose={() => setToast({ ...toast, show: false })} delay={3000} autohide>
          <Toast.Body className="text-white">{toast.message}</Toast.Body>
        </Toast>
      </ToastContainer>
    </Container>
  );
}
