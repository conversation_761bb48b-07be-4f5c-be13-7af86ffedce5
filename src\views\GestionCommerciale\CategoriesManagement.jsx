import React, { useEffect, useState } from 'react';
import {
  fetchCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  fetchAllSousCategories,
  createSousCategorie,
  updateSousCategorie,
  deleteSousCategorie
} from '../../services/categoryService';
import { Table, Button, Form, Alert, Spinner, Container, Row, Col, Card, Tabs, Tab, Badge, Breadcrumb, Modal } from 'react-bootstrap';
import SousSousCategories from './SousSousCategories';
import FeaturedCategories from './FeaturedCategories';
import ImageManager from './ImageManager';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaHome, FaLayerGroup, FaStar } from 'react-icons/fa';

const initialCategoryForm = { nom: '', description: '', image: '' };
const initialSousCategoryForm = { nom: '', description: '', categorie_id: '' };

export default function CategoriesManagement() {
  // Categories
  const [categories, setCategories] = useState([]);
  const [catLoading, setCatLoading] = useState(true);
  const [catError, setCatError] = useState('');
  const [catForm, setCatForm] = useState(initialCategoryForm);
  const [catEditingId, setCatEditingId] = useState(null);
  const [catSubmitting, setCatSubmitting] = useState(false);

  // Sous-categories
  const [sousCategories, setSousCategories] = useState([]);
  const [sousCatLoading, setSousCatLoading] = useState(true);
  const [sousCatError, setSousCatError] = useState('');
  const [sousCatForm, setSousCatForm] = useState(initialSousCategoryForm);
  const [sousCatEditingId, setSousCatEditingId] = useState(null);
  const [sousCatSubmitting, setSousCatSubmitting] = useState(false);

  // Modal states
  const [showCatModal, setShowCatModal] = useState(false);
  const [catModalAction, setCatModalAction] = useState('create'); // 'create' or 'edit'
  const [showSousCatModal, setShowSousCatModal] = useState(false);
  const [sousCatModalAction, setSousCatModalAction] = useState('create'); // 'create' or 'edit'

  // Delete confirmation modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const [tab, setTab] = useState('categories');

  // Load data
  const loadCategories = async () => {
    setCatLoading(true);
    setCatError('');
    try {
      const data = await fetchCategories();
      setCategories(data);
    } catch (e) {
      setCatError(e.message);
    }
    setCatLoading(false);
  };
  const loadSousCategories = async () => {
    setSousCatLoading(true);
    setSousCatError('');
    try {
      const data = await fetchAllSousCategories();
      setSousCategories(data);
    } catch (e) {
      setSousCatError(e.message);
    }
    setSousCatLoading(false);
  };

  useEffect(() => {
    loadCategories();
    loadSousCategories();
  }, []);

  // Category handlers
  const handleCatChange = (e) => setCatForm({ ...catForm, [e.target.name]: e.target.value });
  const handleCatSubmit = async (e) => {
    e.preventDefault();
    setCatSubmitting(true);
    setCatError('');

    try {
      console.log(`📝 ${catEditingId ? 'Updating' : 'Creating'} category:`, catForm);

      if (catEditingId) {
        console.log(`🔄 Updating category with ID: ${catEditingId}`);
        await updateCategory(catEditingId, catForm);
        console.log('✅ Category updated successfully');
      } else {
        console.log('➕ Creating new category');
        await createCategory(catForm);
        console.log('✅ Category created successfully');
      }

      setCatForm(initialCategoryForm);
      setCatEditingId(null);
      setShowCatModal(false);
      loadCategories();
    } catch (e) {
      console.error('❌ Error submitting category:', e);
      setCatError(e.message);
    }
    setCatSubmitting(false);
  };

  // Open category modal for creating
  const handleCatCreate = () => {
    setCatModalAction('create');
    setCatEditingId(null);
    setCatForm(initialCategoryForm);
    setShowCatModal(true);
  };

  // Open category modal for editing
  const handleCatEdit = (cat) => {
    console.log('✏️ Opening category edit modal for:', cat);
    setCatModalAction('edit');
    const formData = {
      nom: cat.nom || cat.nom_categorie,
      description: cat.description || cat.description_categorie,
      image: cat.image || cat.image_categorie
    };
    console.log('📝 Setting category form data:', formData);
    setCatForm(formData);
    setCatEditingId(cat.id);
    setShowCatModal(true);
  };

  // Close category modal
  const handleCatModalClose = () => {
    setShowCatModal(false);
    setCatEditingId(null);
    setCatForm(initialCategoryForm);
    setCatError('');
  };
  // Confirm delete
  const confirmDelete = (type, id, name) => {
    setItemToDelete({ type, id, name });
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!itemToDelete) return;

    setDeleteLoading(true);
    setCatError('');
    setSousCatError('');

    try {
      const { type, id } = itemToDelete;
      console.log(`🗑️ Starting delete process for ${type} with ID: ${id}`);

      if (type === 'category') {
        console.log('📂 Deleting category...');
        await deleteCategory(id);
        console.log('✅ Category deleted, reloading data...');
        loadCategories();
        loadSousCategories(); // update sous-categories if parent deleted
      } else if (type === 'sous-category') {
        console.log('📁 Deleting sous-category...');
        await deleteSousCategorie(id);
        console.log('✅ Sous-category deleted, reloading data...');
        loadSousCategories();
      }

      setShowDeleteModal(false);
      setItemToDelete(null);
    } catch (e) {
      console.error('❌ Delete operation failed:', e);
      if (itemToDelete.type === 'category') {
        setCatError(e.message);
      } else {
        setSousCatError(e.message);
      }
    }

    setDeleteLoading(false);
  };

  // Close delete modal
  const handleDeleteModalClose = () => {
    if (!deleteLoading) {
      setShowDeleteModal(false);
      setItemToDelete(null);
    }
  };

  // Sous-categorie handlers
  const handleSousCatChange = (e) => setSousCatForm({ ...sousCatForm, [e.target.name]: e.target.value });
  const handleSousCatSubmit = async (e) => {
    e.preventDefault();
    setSousCatSubmitting(true);
    setSousCatError('');

    try {
      console.log(`📝 ${sousCatEditingId ? 'Updating' : 'Creating'} sous-category:`, sousCatForm);

      if (sousCatEditingId) {
        console.log(`🔄 Updating sous-category with ID: ${sousCatEditingId}`);
        await updateSousCategorie(sousCatEditingId, sousCatForm);
        console.log('✅ Sous-category updated successfully');
      } else {
        console.log('➕ Creating new sous-category');
        await createSousCategorie(sousCatForm);
        console.log('✅ Sous-category created successfully');
      }

      setSousCatForm(initialSousCategoryForm);
      setSousCatEditingId(null);
      setShowSousCatModal(false);
      loadSousCategories();
    } catch (e) {
      console.error('❌ Error submitting sous-category:', e);
      setSousCatError(e.message);
    }
    setSousCatSubmitting(false);
  };

  // Open subcategory modal for creating
  const handleSousCatCreate = () => {
    setSousCatModalAction('create');
    setSousCatEditingId(null);
    setSousCatForm(initialSousCategoryForm);
    setShowSousCatModal(true);
  };

  // Open subcategory modal for editing
  const handleSousCatEdit = (sc) => {
    console.log('✏️ Opening sous-category edit modal for:', sc);
    setSousCatModalAction('edit');
    const formData = {
      nom: sc.nom || sc.nom_sous_categorie,
      description: sc.description || sc.description_sous_categorie,
      categorie_id: sc.categorie_id
    };
    console.log('📝 Setting sous-category form data:', formData);
    setSousCatForm(formData);
    setSousCatEditingId(sc.id);
    setShowSousCatModal(true);
  };

  // Close subcategory modal
  const handleSousCatModalClose = () => {
    setShowSousCatModal(false);
    setSousCatEditingId(null);
    setSousCatForm(initialSousCategoryForm);
    setSousCatError('');
  };

  return (
    <Container fluid className="py-4">
      {/* Header and Breadcrumb */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaLayerGroup className="me-2" />
          Gestion des Catégories
        </h2>
        <Breadcrumb>
          <Breadcrumb.Item href="/dashboard">
            <FaHome size={14} className="me-1" /> Accueil
          </Breadcrumb.Item>
          <Breadcrumb.Item active>Gestion des Catégories</Breadcrumb.Item>
        </Breadcrumb>
      </div>

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={tab} onSelect={setTab} className="mb-0 nav-tabs-custom" fill>
            <Tab
              eventKey="categories"
              title={
                <span>
                  <Badge bg="primary" pill className="me-2">
                    {categories.length}
                  </Badge>
                  Catégories
                </span>
              }
            />
            <Tab
              eventKey="sous-categories"
              title={
                <span>
                  <Badge bg="success" pill className="me-2">
                    {sousCategories.length}
                  </Badge>
                  Sous-catégories
                </span>
              }
            />
            <Tab eventKey="sous-sous-categories" title="Sous-sous-catégories" />
            <Tab
              eventKey="featured-categories"
              title={
                <span>
                  <FaStar className="me-2 text-warning" />
                  Catégories Mises en Avant
                </span>
              }
            />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Custom CSS for tabs */}
      <style jsx="true">{`
        .nav-tabs-custom .nav-link {
          color: #495057;
          font-weight: 500;
          padding: 1rem 1.5rem;
          border-radius: 0;
          border: none;
          border-bottom: 3px solid transparent;
        }
        .nav-tabs-custom .nav-link.active {
          color: #2196f3;
          background: transparent;
          border-bottom: 3px solid #2196f3;
        }
        .nav-tabs-custom .nav-link:hover:not(.active) {
          border-bottom: 3px solid #e9ecef;
        }
      `}</style>
      {tab === 'categories' && (
        <>
          {catError && <Alert variant="danger">{catError}</Alert>}
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h5 className="mb-0 fw-bold">Liste des catégories</h5>
              <div className="d-flex align-items-center gap-3">
                <div className="text-muted small">
                  {categories.length} catégorie{categories.length !== 1 ? 's' : ''} au total
                </div>
                <Button variant="primary" size="sm" onClick={handleCatCreate}>
                  <FaPlus className="me-1" />
                  Ajouter une catégorie
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {catLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-3 text-muted">Chargement des catégories...</p>
                </div>
              ) : categories.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <i className="bi bi-folder text-muted" style={{ fontSize: '3rem' }}></i>
                  </div>
                  <p className="text-muted">Aucune catégorie trouvée.</p>
                  <Button variant="primary" onClick={handleCatCreate}>
                    <FaPlus className="me-2" />
                    Créer une catégorie
                  </Button>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table hover responsive className="align-middle mb-0">
                    <thead>
                      <tr className="bg-light">
                        <th className="ps-3" style={{ width: '60px' }}>
                          ID
                        </th>
                        <th style={{ width: '25%' }}>Nom</th>
                        <th style={{ width: '35%' }}>Description</th>
                        <th style={{ width: '20%' }}>Image</th>
                        <th className="text-end pe-3" style={{ width: '20%' }}>
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {categories.map((cat) => (
                        <tr key={cat.id} className="border-bottom">
                          <td className="ps-3 fw-medium">{cat.id}</td>
                          <td>
                            <div className="d-flex align-items-center">
                              <div className="color-dot bg-primary me-2"></div>
                              <span className="fw-medium">{cat.nom || cat.nom_categorie}</span>
                            </div>
                          </td>
                          <td>
                            <div className="text-truncate" style={{ maxWidth: '300px' }}>
                              {cat.description || cat.description_categorie || (
                                <span className="text-muted fst-italic">Aucune description</span>
                              )}
                            </div>
                          </td>
                          <td>
                            {cat.image || cat.image_categorie ? (
                              <div className="d-flex align-items-center">
                                <div className="image-thumbnail me-2">
                                  <img
                                    src={cat.image || cat.image_categorie}
                                    alt={cat.nom || cat.nom_categorie}
                                    onError={(e) => {
                                      e.target.src = 'https://via.placeholder.com/40';
                                    }}
                                    width="40"
                                    height="40"
                                    className="rounded"
                                  />
                                </div>
                                <div className="text-truncate" style={{ maxWidth: '150px' }}>
                                  <small className="text-muted">{cat.image || cat.image_categorie}</small>
                                </div>
                              </div>
                            ) : (
                              <span className="text-muted fst-italic">Aucune image</span>
                            )}
                          </td>
                          <td className="text-end pe-3">
                            <Button
                              size="sm"
                              variant="outline-primary"
                              className="me-2 rounded-pill"
                              onClick={() => handleCatEdit(cat)}
                              title="Éditer la catégorie"
                            >
                              <FaPencilAlt className="me-1" /> Éditer
                            </Button>
                            <Button
                              size="sm"
                              variant="outline-danger"
                              className="rounded-pill"
                              onClick={() => confirmDelete('category', cat.id, cat.nom || cat.nom_categorie)}
                              title="Supprimer la catégorie"
                            >
                              <FaTrashAlt className="me-1" /> Supprimer
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Custom CSS for table styling */}
          <style jsx="true">{`
            .color-dot {
              width: 10px;
              height: 10px;
              border-radius: 50%;
            }
            .image-thumbnail img {
              object-fit: cover;
            }
          `}</style>
        </>
      )}
      {tab === 'sous-categories' && (
        <>
          {sousCatError && <Alert variant="danger">{sousCatError}</Alert>}
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h5 className="mb-0 fw-bold">Liste des sous-catégories</h5>
              <div className="d-flex align-items-center gap-3">
                <div className="text-muted small">
                  {sousCategories.length} sous-catégorie{sousCategories.length !== 1 ? 's' : ''} au total
                </div>
                <Button variant="success" size="sm" onClick={handleSousCatCreate}>
                  <FaPlus className="me-1" />
                  Ajouter une sous-catégorie
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {sousCatLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="success" />
                  <p className="mt-3 text-muted">Chargement des sous-catégories...</p>
                </div>
              ) : sousCategories.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <i className="bi bi-folder text-muted" style={{ fontSize: '3rem' }}></i>
                  </div>
                  <p className="text-muted">Aucune sous-catégorie trouvée.</p>
                  <Button variant="success" onClick={handleSousCatCreate}>
                    <FaPlus className="me-2" />
                    Créer une sous-catégorie
                  </Button>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table hover responsive className="align-middle mb-0">
                    <thead>
                      <tr className="bg-light">
                        <th className="ps-3" style={{ width: '60px' }}>
                          ID
                        </th>
                        <th style={{ width: '20%' }}>Nom</th>
                        <th style={{ width: '30%' }}>Description</th>
                        <th style={{ width: '25%' }}>Catégorie parente</th>
                        <th className="text-end pe-3" style={{ width: '25%' }}>
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {sousCategories.map((sc) => {
                        const parentCat = categories.find((cat) => cat.id === parseInt(sc.categorie_id));
                        return (
                          <tr key={sc.id} className="border-bottom">
                            <td className="ps-3 fw-medium">{sc.id}</td>
                            <td>
                              <div className="d-flex align-items-center">
                                <div className="color-dot bg-success me-2"></div>
                                <span className="fw-medium">{sc.nom || sc.nom_sous_categorie}</span>
                              </div>
                            </td>
                            <td>
                              <div className="text-truncate" style={{ maxWidth: '300px' }}>
                                {sc.description || sc.description_sous_categorie || (
                                  <span className="text-muted fst-italic">Aucune description</span>
                                )}
                              </div>
                            </td>
                            <td>
                              {parentCat ? (
                                <div className="d-flex align-items-center">
                                  <div className="color-dot bg-primary me-2"></div>
                                  <span>{parentCat.nom || parentCat.nom_categorie}</span>
                                </div>
                              ) : (
                                <span className="text-muted">{sc.categorie_id}</span>
                              )}
                            </td>
                            <td className="text-end pe-3">
                              <Button
                                size="sm"
                                variant="outline-primary"
                                className="me-2 rounded-pill"
                                onClick={() => handleSousCatEdit(sc)}
                                title="Éditer la sous-catégorie"
                              >
                                <FaPencilAlt className="me-1" /> Éditer
                              </Button>
                              <Button
                                size="sm"
                                variant="outline-danger"
                                className="rounded-pill"
                                onClick={() => confirmDelete('sous-category', sc.id, sc.nom || sc.nom_sous_categorie)}
                                title="Supprimer la sous-catégorie"
                              >
                                <FaTrashAlt className="me-1" /> Supprimer
                              </Button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>
        </>
      )}
      {tab === 'sous-sous-categories' && <SousSousCategories ImageManager={ImageManager} />}
      {tab === 'featured-categories' && <FeaturedCategories />}

      {/* Category Modal */}
      <Modal show={showCatModal} onHide={handleCatModalClose} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className={`fas ${catModalAction === 'edit' ? 'fa-edit' : 'fa-plus'} me-2`}></i>
            {catModalAction === 'edit' ? 'Modifier une catégorie' : 'Ajouter une nouvelle catégorie'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleCatSubmit}>
            <Row className="g-3">
              <Col xs={12} md={6}>
                <Form.Group controlId="categoryNom">
                  <Form.Label className="fw-medium">Nom de la catégorie</Form.Label>
                  <Form.Control
                    name="nom"
                    value={catForm.nom || ''}
                    onChange={handleCatChange}
                    placeholder="Nom de la catégorie"
                    required
                    disabled={catSubmitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
                </Form.Group>
              </Col>
              <Col xs={12} md={6}>
                <Form.Group controlId="categoryImage">
                  <Form.Label className="fw-medium">Image (URL)</Form.Label>
                  <Form.Control
                    name="image"
                    value={catForm.image || ''}
                    onChange={handleCatChange}
                    placeholder="ex: https://..."
                    disabled={catSubmitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">URL de l'image représentant la catégorie.</Form.Text>
                </Form.Group>
              </Col>
            </Row>
            <Row className="mt-3">
              <Col xs={12}>
                <Form.Group controlId="categoryDescription">
                  <Form.Label className="fw-medium">Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={catForm.description || ''}
                    onChange={handleCatChange}
                    placeholder="Description détaillée de la catégorie"
                    disabled={catSubmitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCatModalClose} disabled={catSubmitting}>
            Annuler
          </Button>
          <Button variant={catModalAction === 'edit' ? 'warning' : 'primary'} onClick={handleCatSubmit} disabled={catSubmitting}>
            {catSubmitting ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Traitement...
              </>
            ) : catModalAction === 'edit' ? (
              <>
                <FaPencilAlt className="me-2" />
                Mettre à jour
              </>
            ) : (
              <>
                <FaPlus className="me-2" />
                Ajouter
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Subcategory Modal */}
      <Modal show={showSousCatModal} onHide={handleSousCatModalClose} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className={`fas ${sousCatModalAction === 'edit' ? 'fa-edit' : 'fa-plus'} me-2`}></i>
            {sousCatModalAction === 'edit' ? 'Modifier une sous-catégorie' : 'Ajouter une nouvelle sous-catégorie'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSousCatSubmit}>
            <Row className="g-3">
              <Col xs={12} md={6}>
                <Form.Group controlId="sousCategorieNom">
                  <Form.Label className="fw-medium">Nom de la sous-catégorie</Form.Label>
                  <Form.Control
                    name="nom"
                    value={sousCatForm.nom || ''}
                    onChange={handleSousCatChange}
                    placeholder="Nom de la sous-catégorie"
                    required
                    disabled={sousCatSubmitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
                </Form.Group>
              </Col>
              <Col xs={12} md={6}>
                <Form.Group controlId="categorieId">
                  <Form.Label className="fw-medium">Catégorie parente</Form.Label>
                  <Form.Select
                    name="categorie_id"
                    value={sousCatForm.categorie_id || ''}
                    onChange={handleSousCatChange}
                    required
                    disabled={sousCatSubmitting || categories.length === 0}
                    className="rounded-3 border-2"
                  >
                    <option value="">Sélectionnez une catégorie</option>
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.nom || cat.nom_categorie}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">Sélectionnez la catégorie à laquelle cette sous-catégorie appartient.</Form.Text>
                </Form.Group>
              </Col>
            </Row>
            <Row className="mt-3">
              <Col xs={12}>
                <Form.Group controlId="sousCategorieDescription">
                  <Form.Label className="fw-medium">Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={sousCatForm.description || ''}
                    onChange={handleSousCatChange}
                    placeholder="Description détaillée de la sous-catégorie"
                    disabled={sousCatSubmitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleSousCatModalClose} disabled={sousCatSubmitting}>
            Annuler
          </Button>
          <Button
            variant={sousCatModalAction === 'edit' ? 'warning' : 'success'}
            onClick={handleSousCatSubmit}
            disabled={sousCatSubmitting}
          >
            {sousCatSubmitting ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Traitement...
              </>
            ) : sousCatModalAction === 'edit' ? (
              <>
                <FaPencilAlt className="me-2" />
                Mettre à jour
              </>
            ) : (
              <>
                <FaPlus className="me-2" />
                Ajouter
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={handleDeleteModalClose} centered>
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="text-danger">
            <FaTrashAlt className="me-2" />
            Confirmer la suppression
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="pt-0">
          {itemToDelete && (
            <div className="text-center py-3">
              <div className="mb-3">
                <div
                  className="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center"
                  style={{ width: '60px', height: '60px' }}
                >
                  <FaTrashAlt className="text-danger" size={24} />
                </div>
              </div>
              <h5 className="mb-3">Êtes-vous sûr de vouloir supprimer ?</h5>
              <p className="text-muted mb-0">
                <strong>{itemToDelete.type === 'category' ? 'Catégorie' : 'Sous-catégorie'} :</strong> {itemToDelete.name}
              </p>
              <p className="text-muted small mt-2">
                {itemToDelete.type === 'category'
                  ? 'Cette action supprimera également toutes les sous-catégories et produits associés.'
                  : 'Cette action supprimera également tous les produits associés à cette sous-catégorie.'}
              </p>
              <div className="alert alert-warning mt-3 mb-0">
                <small>
                  <strong>⚠️ Attention :</strong> Cette action est irréversible.
                </small>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="border-0 pt-0">
          <Button variant="secondary" onClick={handleDeleteModalClose} disabled={deleteLoading}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleDelete} disabled={deleteLoading}>
            {deleteLoading ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Suppression...
              </>
            ) : (
              <>
                <FaTrashAlt className="me-2" />
                Supprimer définitivement
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
}
